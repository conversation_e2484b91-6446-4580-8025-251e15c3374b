// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		5909BB832E1F310900B54F22 /* SwiftGodotKit in Frameworks */ = {isa = PBXBuildFile; productRef = 5909BB822E1F310900B54F22 /* SwiftGodotKit */; };
		596133982E1431220062D5A4 /* RiveRuntime in Frameworks */ = {isa = PBXBuildFile; productRef = 596133972E1431220062D5A4 /* RiveRuntime */; };
		59D5E8302E1F60EB009F6C7E /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 59B724762E1F5AD70075BD1F /* AudioToolbox.framework */; };
		59D5E8312E1F60EB009F6C7E /* AudioToolbox.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 59B724762E1F5AD70075BD1F /* AudioToolbox.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		59D5E8332E1F60EC009F6C7E /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 596B0D6B2E1F58B7002249BD /* AudioUnit.framework */; };
		59D5E8342E1F60EC009F6C7E /* AudioUnit.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 596B0D6B2E1F58B7002249BD /* AudioUnit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		59D5E8352E1F60F8009F6C7E /* AVFAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 59B724742E1F5ACF0075BD1F /* AVFAudio.framework */; };
		59D5E8362E1F60F8009F6C7E /* AVFAudio.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 59B724742E1F5ACF0075BD1F /* AVFAudio.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		59D5E8372E1F60F9009F6C7E /* CoreAudioKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 596B0D712E1F598A002249BD /* CoreAudioKit.framework */; };
		59D5E8382E1F60F9009F6C7E /* CoreAudioKit.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 596B0D712E1F598A002249BD /* CoreAudioKit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		59D5E8392E1F60FD009F6C7E /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 598D79DC2E1F4D7B00C8E336 /* Metal.framework */; };
		59D5E83A2E1F60FD009F6C7E /* Metal.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 598D79DC2E1F4D7B00C8E336 /* Metal.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		59D5E83B2E1F60FE009F6C7E /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 598D79DE2E1F4D8500C8E336 /* MetalKit.framework */; };
		59D5E83C2E1F60FE009F6C7E /* MetalKit.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 598D79DE2E1F4D8500C8E336 /* MetalKit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		5961336C2E142C3C0062D5A4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 596133542E142C3B0062D5A4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 5961335B2E142C3B0062D5A4;
			remoteInfo = focusflyer;
		};
		596133762E142C3C0062D5A4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 596133542E142C3B0062D5A4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 5961335B2E142C3B0062D5A4;
			remoteInfo = focusflyer;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		59D5E8322E1F60EB009F6C7E /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				59D5E8362E1F60F8009F6C7E /* AVFAudio.framework in Embed Frameworks */,
				59D5E8382E1F60F9009F6C7E /* CoreAudioKit.framework in Embed Frameworks */,
				59D5E83A2E1F60FD009F6C7E /* Metal.framework in Embed Frameworks */,
				59D5E83C2E1F60FE009F6C7E /* MetalKit.framework in Embed Frameworks */,
				59D5E8312E1F60EB009F6C7E /* AudioToolbox.framework in Embed Frameworks */,
				59D5E8342E1F60EC009F6C7E /* AudioUnit.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		5961335C2E142C3B0062D5A4 /* focusflyer.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = focusflyer.app; sourceTree = BUILT_PRODUCTS_DIR; };
		5961336B2E142C3C0062D5A4 /* focusflyerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = focusflyerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		596133752E142C3C0062D5A4 /* focusflyerUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = focusflyerUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		596B0D6B2E1F58B7002249BD /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = System/Library/Frameworks/AudioUnit.framework; sourceTree = SDKROOT; };
		596B0D6D2E1F58C1002249BD /* CoreAudioTypes.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioTypes.framework; path = System/Library/Frameworks/CoreAudioTypes.framework; sourceTree = SDKROOT; };
		596B0D6F2E1F595B002249BD /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		596B0D712E1F598A002249BD /* CoreAudioKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioKit.framework; path = System/Library/Frameworks/CoreAudioKit.framework; sourceTree = SDKROOT; };
		598D79DC2E1F4D7B00C8E336 /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		598D79DE2E1F4D8500C8E336 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		59B724742E1F5ACF0075BD1F /* AVFAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFAudio.framework; path = System/Library/Frameworks/AVFAudio.framework; sourceTree = SDKROOT; };
		59B724762E1F5AD70075BD1F /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		5961335E2E142C3B0062D5A4 /* focusflyer */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = focusflyer;
			sourceTree = "<group>";
		};
		5961336E2E142C3C0062D5A4 /* focusflyerTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = focusflyerTests;
			sourceTree = "<group>";
		};
		596133782E142C3C0062D5A4 /* focusflyerUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = focusflyerUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		596133592E142C3B0062D5A4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				59D5E83B2E1F60FE009F6C7E /* MetalKit.framework in Frameworks */,
				59D5E8392E1F60FD009F6C7E /* Metal.framework in Frameworks */,
				59D5E8302E1F60EB009F6C7E /* AudioToolbox.framework in Frameworks */,
				59D5E8372E1F60F9009F6C7E /* CoreAudioKit.framework in Frameworks */,
				596133982E1431220062D5A4 /* RiveRuntime in Frameworks */,
				59D5E8352E1F60F8009F6C7E /* AVFAudio.framework in Frameworks */,
				5909BB832E1F310900B54F22 /* SwiftGodotKit in Frameworks */,
				59D5E8332E1F60EC009F6C7E /* AudioUnit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		596133682E142C3C0062D5A4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		596133722E142C3C0062D5A4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		596133532E142C3B0062D5A4 = {
			isa = PBXGroup;
			children = (
				5961335E2E142C3B0062D5A4 /* focusflyer */,
				5961336E2E142C3C0062D5A4 /* focusflyerTests */,
				596133782E142C3C0062D5A4 /* focusflyerUITests */,
				598D79DB2E1F4D7B00C8E336 /* Frameworks */,
				5961335D2E142C3B0062D5A4 /* Products */,
			);
			sourceTree = "<group>";
		};
		5961335D2E142C3B0062D5A4 /* Products */ = {
			isa = PBXGroup;
			children = (
				5961335C2E142C3B0062D5A4 /* focusflyer.app */,
				5961336B2E142C3C0062D5A4 /* focusflyerTests.xctest */,
				596133752E142C3C0062D5A4 /* focusflyerUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		598D79DB2E1F4D7B00C8E336 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				59B724762E1F5AD70075BD1F /* AudioToolbox.framework */,
				59B724742E1F5ACF0075BD1F /* AVFAudio.framework */,
				596B0D712E1F598A002249BD /* CoreAudioKit.framework */,
				596B0D6F2E1F595B002249BD /* CoreVideo.framework */,
				596B0D6D2E1F58C1002249BD /* CoreAudioTypes.framework */,
				596B0D6B2E1F58B7002249BD /* AudioUnit.framework */,
				598D79DE2E1F4D8500C8E336 /* MetalKit.framework */,
				598D79DC2E1F4D7B00C8E336 /* Metal.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		5961335B2E142C3B0062D5A4 /* focusflyer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5961337F2E142C3C0062D5A4 /* Build configuration list for PBXNativeTarget "focusflyer" */;
			buildPhases = (
				596133582E142C3B0062D5A4 /* Sources */,
				596133592E142C3B0062D5A4 /* Frameworks */,
				5961335A2E142C3B0062D5A4 /* Resources */,
				59D5E8322E1F60EB009F6C7E /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				5961335E2E142C3B0062D5A4 /* focusflyer */,
			);
			name = focusflyer;
			packageProductDependencies = (
				596133972E1431220062D5A4 /* RiveRuntime */,
				5909BB822E1F310900B54F22 /* SwiftGodotKit */,
			);
			productName = focusflyer;
			productReference = 5961335C2E142C3B0062D5A4 /* focusflyer.app */;
			productType = "com.apple.product-type.application";
		};
		5961336A2E142C3C0062D5A4 /* focusflyerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 596133822E142C3C0062D5A4 /* Build configuration list for PBXNativeTarget "focusflyerTests" */;
			buildPhases = (
				596133672E142C3C0062D5A4 /* Sources */,
				596133682E142C3C0062D5A4 /* Frameworks */,
				596133692E142C3C0062D5A4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5961336D2E142C3C0062D5A4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				5961336E2E142C3C0062D5A4 /* focusflyerTests */,
			);
			name = focusflyerTests;
			packageProductDependencies = (
			);
			productName = focusflyerTests;
			productReference = 5961336B2E142C3C0062D5A4 /* focusflyerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		596133742E142C3C0062D5A4 /* focusflyerUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 596133852E142C3C0062D5A4 /* Build configuration list for PBXNativeTarget "focusflyerUITests" */;
			buildPhases = (
				596133712E142C3C0062D5A4 /* Sources */,
				596133722E142C3C0062D5A4 /* Frameworks */,
				596133732E142C3C0062D5A4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				596133772E142C3C0062D5A4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				596133782E142C3C0062D5A4 /* focusflyerUITests */,
			);
			name = focusflyerUITests;
			packageProductDependencies = (
			);
			productName = focusflyerUITests;
			productReference = 596133752E142C3C0062D5A4 /* focusflyerUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		596133542E142C3B0062D5A4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					5961335B2E142C3B0062D5A4 = {
						CreatedOnToolsVersion = 16.4;
					};
					5961336A2E142C3C0062D5A4 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 5961335B2E142C3B0062D5A4;
					};
					596133742E142C3C0062D5A4 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 5961335B2E142C3B0062D5A4;
					};
				};
			};
			buildConfigurationList = 596133572E142C3B0062D5A4 /* Build configuration list for PBXProject "focusflyer" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 596133532E142C3B0062D5A4;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				596133962E1431220062D5A4 /* XCRemoteSwiftPackageReference "rive-ios" */,
				5909BB812E1F310900B54F22 /* XCRemoteSwiftPackageReference "SwiftGodotKit" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 5961335D2E142C3B0062D5A4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				5961335B2E142C3B0062D5A4 /* focusflyer */,
				5961336A2E142C3C0062D5A4 /* focusflyerTests */,
				596133742E142C3C0062D5A4 /* focusflyerUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		5961335A2E142C3B0062D5A4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		596133692E142C3C0062D5A4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		596133732E142C3C0062D5A4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		596133582E142C3B0062D5A4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		596133672E142C3C0062D5A4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		596133712E142C3C0062D5A4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		5961336D2E142C3C0062D5A4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 5961335B2E142C3B0062D5A4 /* focusflyer */;
			targetProxy = 5961336C2E142C3C0062D5A4 /* PBXContainerItemProxy */;
		};
		596133772E142C3C0062D5A4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 5961335B2E142C3B0062D5A4 /* focusflyer */;
			targetProxy = 596133762E142C3C0062D5A4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		5961337D2E142C3C0062D5A4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		5961337E2E142C3C0062D5A4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		596133802E142C3C0062D5A4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = FocusFlyer;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.focusflyer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		596133812E142C3C0062D5A4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = FocusFlyer;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.focusflyer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		596133832E142C3C0062D5A4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.focusflyerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/focusflyer.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/focusflyer";
			};
			name = Debug;
		};
		596133842E142C3C0062D5A4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.focusflyerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/focusflyer.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/focusflyer";
			};
			name = Release;
		};
		596133862E142C3C0062D5A4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.focusflyerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = focusflyer;
			};
			name = Debug;
		};
		596133872E142C3C0062D5A4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GJW99ZB9LQ;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = men.aisky.focusflyerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = focusflyer;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		596133572E142C3B0062D5A4 /* Build configuration list for PBXProject "focusflyer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5961337D2E142C3C0062D5A4 /* Debug */,
				5961337E2E142C3C0062D5A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5961337F2E142C3C0062D5A4 /* Build configuration list for PBXNativeTarget "focusflyer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				596133802E142C3C0062D5A4 /* Debug */,
				596133812E142C3C0062D5A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		596133822E142C3C0062D5A4 /* Build configuration list for PBXNativeTarget "focusflyerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				596133832E142C3C0062D5A4 /* Debug */,
				596133842E142C3C0062D5A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		596133852E142C3C0062D5A4 /* Build configuration list for PBXNativeTarget "focusflyerUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				596133862E142C3C0062D5A4 /* Debug */,
				596133872E142C3C0062D5A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		5909BB812E1F310900B54F22 /* XCRemoteSwiftPackageReference "SwiftGodotKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/migueldeicaza/SwiftGodotKit.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
		596133962E1431220062D5A4 /* XCRemoteSwiftPackageReference "rive-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/rive-app/rive-ios.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.9.4;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		5909BB822E1F310900B54F22 /* SwiftGodotKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5909BB812E1F310900B54F22 /* XCRemoteSwiftPackageReference "SwiftGodotKit" */;
			productName = SwiftGodotKit;
		};
		596133972E1431220062D5A4 /* RiveRuntime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 596133962E1431220062D5A4 /* XCRemoteSwiftPackageReference "rive-ios" */;
			productName = RiveRuntime;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 596133542E142C3B0062D5A4 /* Project object */;
}
