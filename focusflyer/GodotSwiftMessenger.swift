//
//  GodotSwiftMessenger.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftGodot

@Godot
class GodotSwiftMessenger: Object {
    public static let shared = GodotSwiftMessenger()
    
    // Signal to close the Godot view from within Godot
    @Signal var closeGodotView: Signal
    
    // Signal to send messages from iOS to Godot
    @Signal var messageFromiOS: SignalWithArguments<String>
    
    // Signal to send messages from Godot to iOS
    @Signal var messageFromGodot: SignalWithArguments<String>
    
    private override init() {
        super.init()
    }
}
