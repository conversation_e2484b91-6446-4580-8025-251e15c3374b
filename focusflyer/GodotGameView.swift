//
//  GodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftG<PERSON>otKit
import SwiftGodot

struct GodotGameView: View {
    @State private var godotApp: GodotApp?
    @State private var errorMessage: String?
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        NavigationView {
            ZStack {
                if let godotApp = godotApp {
                    // Godot game view
                    GodotAppView()
                        .environment(\.godotApp, godotApp)
                        .ignoresSafeArea()
                } else if let errorMessage = errorMessage {
                    // Error view
                    VStack(spacing: 20) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)

                        Text("Failed to load Godot game")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(errorMessage)
                            .font(.body)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        Button("Try Again") {
                            loadGodotGame()
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding()
                } else {
                    // Loading view
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.5)

                        Text("Loading Godot Game...")
                            .font(.title2)
                    }
                }

                // Close button overlay
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            isPresented = false
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title)
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                        .padding()
                    }
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            loadGodotGame()
        }
    }

    private func loadGodotGame() {
        print("Loading Godot game...")

        // Check if the pck file exists
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            print("Game file 'test2.pck' not found in app bundle")
            // Try to create a minimal Godot app without a pck file
            loadMinimalGodotApp()
            return
        }

        print("Found pck file at: \(pckPath)")

        do {
            // Try to initialize the Godot app
            let app = GodotApp(packFile: "test2.pck")
            self.godotApp = app
            print("Successfully loaded Godot game")
        } catch {
            print("Error loading Godot with pck: \(error)")
            // Fallback to minimal app
            loadMinimalGodotApp()
        }
    }

    private func loadMinimalGodotApp() {
        print("Loading minimal Godot app...")

        do {
            // Create a Godot app without a specific pck file
            let app = GodotApp()
            self.godotApp = app
            print("Successfully loaded minimal Godot app")
        } catch {
            errorMessage = "Failed to initialize Godot: \(error.localizedDescription)"
            print("Error loading minimal Godot: \(error)")
        }
    }
}

#Preview {
    GodotGameView(isPresented: .constant(true))
}
