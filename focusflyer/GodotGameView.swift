//
//  GodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct GodotGameView: View {
    @State private var godotApp: GodotApp
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented

        // Initialize with the test2.pck file
        self._godotApp = State(initialValue: GodotApp(packFile: "test2.pck"))
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Godot game view
                GodotAppView()
                    .environment(\.godotApp, godotApp)
                    .ignoresSafeArea()

                // Close button overlay
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            isPresented = false
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title)
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                        .padding()
                    }
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupGodotSignals()
        }
    }

    private func setupGodotSignals() {
        // TODO: Setup Godot signals when needed
        print("Setting up Godot signals")
    }
}

#Preview {
    GodotGameView(isPresented: .constant(true))
}
