//
//  focusflyerApp.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import SwiftUI
import SwiftData
import SwiftGodot

@main
struct focusflyerApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Item.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    init() {
        // Setup Godot integration
        initHookCb = { level in
            guard level == .scene else { return }

            register(type: GodotSwiftMessenger.self)
            Engine.registerSingleton(name: "GodotSwiftMessenger", instance: GodotSwiftMessenger.shared)
        }
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .modelContainer(sharedModelContainer)
    }
}
