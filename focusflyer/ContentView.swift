//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI

struct ContentView: View {
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")

    var body: some View {
        VStack(spacing: 24) {
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)
        }
        .padding()
    }
}

#Preview {
  ContentView()
}
